import React, {
  useState,
  useRef,
  useCallback,
  useEffect,
  useMemo,
} from "react";
import { cn } from "@/lib/utils";

export const KNXAddressInput = ({
  value = "",
  onChange,
  onComplete,
  disabled = false,
  error = false,
  placeholder = "0/0/0",
  className,
  showValidationHints = false,
  size = "default",
  ...props
}) => {
  const [values, setValues] = useState(() => {
    const parts = value.split(/[./]/).filter(Boolean);
    return {
      area: parts[0] || "",
      line: parts[1] || "",
      device: parts[2] || "",
    };
  });

  const [focusedField, setFocusedField] = useState(null);
  const [hasInteracted, setHasInteracted] = useState(false);

  const refs = {
    area: useRef(null),
    line: useRef(null),
    device: useRef(null),
  };

  // Validation configuration
  const validationRules = useMemo(
    () => ({
      area: { min: 0, max: 31, bits: 5, label: "Area" },
      line: { min: 0, max: 7, bits: 3, label: "Line" },
      device: { min: 0, max: 255, bits: 8, label: "Device" },
    }),
    []
  );

  const validateInput = useCallback(
    (type, inputValue) => {
      if (!inputValue) return true; // Empty is valid during input
      const num = parseInt(inputValue, 10);
      if (isNaN(num)) return false;

      const rule = validationRules[type];
      return num >= rule.min && num <= rule.max;
    },
    [validationRules]
  );

  // Sync external value changes
  useEffect(() => {
    const parts = value.split(/[./]/).filter(Boolean);
    const newValues = {
      area: parts[0] || "",
      line: parts[1] || "",
      device: parts[2] || "",
    };

    // Only update if values actually changed to prevent infinite loops
    if (JSON.stringify(newValues) !== JSON.stringify(values)) {
      setValues(newValues);
    }
  }, [value]); // Remove values dependency to prevent infinite loop

  const updateValue = useCallback(
    (field, newValue) => {
      setHasInteracted(true);
      const updatedValues = { ...values, [field]: newValue };
      setValues(updatedValues);

      // Always use / separator for output to match KNX standard
      const fullAddress = `${updatedValues.area}/${updatedValues.line}/${updatedValues.device}`;
      onChange?.(fullAddress);

      // Check if address is complete and valid
      if (updatedValues.area && updatedValues.line && updatedValues.device) {
        const isValid =
          validateInput("area", updatedValues.area) &&
          validateInput("line", updatedValues.line) &&
          validateInput("device", updatedValues.device);
        if (isValid) {
          onComplete?.(fullAddress);
        }
      }
    },
    [values, onChange, onComplete, validateInput]
  );

  const handleInputChange = useCallback(
    (field, event) => {
      const inputValue = event.target.value;

      // Only allow numbers
      if (!/^\d*$/.test(inputValue)) return;

      // Allow any numeric input during typing - validation happens on blur/focus change
      updateValue(field, inputValue);

      // Auto-focus next input when reaching max length for each field
      const maxLengths = { area: 2, line: 1, device: 3 };
      if (inputValue.length === maxLengths[field]) {
        // Only auto-advance if the current value is valid
        if (validateInput(field, inputValue)) {
          const nextField =
            field === "area" ? "line" : field === "line" ? "device" : null;
          if (nextField && refs[nextField].current) {
            setTimeout(() => refs[nextField].current.focus(), 0);
          }
        }
      }
    },
    [updateValue, validateInput]
  );

  const handleKeyDown = useCallback(
    (field, event) => {
      // Handle "." and "/" to move to next field
      if (event.key === "." || event.key === "/") {
        event.preventDefault();
        const nextField =
          field === "area" ? "line" : field === "line" ? "device" : null;
        if (nextField && refs[nextField].current) {
          refs[nextField].current.focus();
          refs[nextField].current.select();
        }
        return;
      }

      // Enhanced backspace navigation
      if (event.key === "Backspace" && !values[field]) {
        event.preventDefault();
        const prevField =
          field === "device" ? "line" : field === "line" ? "area" : null;
        if (prevField && refs[prevField].current) {
          refs[prevField].current.focus();
          refs[prevField].current.select();
        }
      }

      // Arrow key navigation
      if (event.key === "ArrowLeft" && event.target.selectionStart === 0) {
        event.preventDefault();
        const prevField =
          field === "device" ? "line" : field === "line" ? "area" : null;
        if (prevField && refs[prevField].current) {
          refs[prevField].current.focus();
          refs[prevField].current.setSelectionRange(
            refs[prevField].current.value.length,
            refs[prevField].current.value.length
          );
        }
      }

      if (
        event.key === "ArrowRight" &&
        event.target.selectionStart === event.target.value.length
      ) {
        event.preventDefault();
        const nextField =
          field === "area" ? "line" : field === "line" ? "device" : null;
        if (nextField && refs[nextField].current) {
          refs[nextField].current.focus();
          refs[nextField].current.setSelectionRange(0, 0);
        }
      }

      // Tab navigation enhancement
      if (event.key === "Tab" && !event.shiftKey) {
        const nextField =
          field === "area" ? "line" : field === "line" ? "device" : null;
        if (nextField && refs[nextField].current) {
          event.preventDefault();
          refs[nextField].current.focus();
          refs[nextField].current.select();
        }
      }

      if (event.key === "Tab" && event.shiftKey) {
        const prevField =
          field === "device" ? "line" : field === "line" ? "area" : null;
        if (prevField && refs[prevField].current) {
          event.preventDefault();
          refs[prevField].current.focus();
          refs[prevField].current.select();
        }
      }
    },
    [values]
  );

  const handlePaste = useCallback(
    (event) => {
      event.preventDefault();
      const pastedText = event.clipboardData.getData("text").trim();

      // Handle both . and / separators
      const parts = pastedText.split(/[./]/).filter(Boolean);

      if (parts.length === 3) {
        const [area, line, device] = parts;
        if (
          validateInput("area", area) &&
          validateInput("line", line) &&
          validateInput("device", device)
        ) {
          setHasInteracted(true);
          setValues({ area, line, device });
          const fullAddress = `${area}/${line}/${device}`;
          onChange?.(fullAddress);
          onComplete?.(fullAddress);

          // Focus the last field after paste
          setTimeout(() => {
            if (refs.device.current) {
              refs.device.current.focus();
              refs.device.current.select();
            }
          }, 0);
        }
      }
    },
    [validateInput, onChange, onComplete]
  );

  const handleFocus = useCallback((field) => {
    setFocusedField(field);
    setHasInteracted(true);
  }, []);

  const handleBlur = useCallback(
    (field) => {
      setFocusedField(null);
      // Trigger validation when user leaves the field
      if (values[field] && !validateInput(field, values[field])) {
        // Field has invalid value - validation error will show
        setHasInteracted(true);
      }
    },
    [values, validateInput]
  );

  const getFieldError = useCallback(
    (field) => {
      // Only show error if user has interacted, field has value, and field is not currently focused
      if (!hasInteracted || !values[field] || focusedField === field)
        return false;
      return !validateInput(field, values[field]);
    },
    [hasInteracted, values, validateInput, focusedField]
  );

  const isComplete = useMemo(() => {
    return (
      values.area &&
      values.line &&
      values.device &&
      validateInput("area", values.area) &&
      validateInput("line", values.line) &&
      validateInput("device", values.device)
    );
  }, [values, validateInput]);

  // Size variants
  const sizeClasses = {
    sm: "h-8 text-xs",
    default: "h-9 text-sm",
    lg: "h-10 text-base",
  };

  const fieldWidths = {
    area: size === "sm" ? "w-10" : size === "lg" ? "w-14" : "w-12",
    line: size === "sm" ? "w-10" : size === "lg" ? "w-14" : "w-12",
    device: size === "sm" ? "w-10" : size === "lg" ? "w-14" : "w-12",
  };

  const renderInput = (field, maxLength) => {
    const hasError = error || getFieldError(field);
    const isFocused = focusedField === field;
    const rule = validationRules[field];

    return (
      <div className="relative">
        <input
          ref={refs[field]}
          type="text"
          value={values[field]}
          onChange={(e) => handleInputChange(field, e)}
          onKeyDown={(e) => handleKeyDown(field, e)}
          onFocus={() => handleFocus(field)}
          onBlur={() => handleBlur(field)}
          onPaste={handlePaste}
          disabled={disabled}
          placeholder="0"
          maxLength={maxLength}
          aria-label={`KNX ${rule.label} (${rule.min}-${rule.max})`}
          aria-invalid={hasError}
          aria-describedby={
            showValidationHints ? `knx-${field}-hint` : undefined
          }
          className={cn(
            // Base styles following the design system
            "text-center border rounded-md transition-all duration-200",
            "focus:outline-none focus-visible:ring-ring/50 focus-visible:ring-[3px]",
            "placeholder:text-muted-foreground",
            sizeClasses[size],
            fieldWidths[field],

            // State-based styles
            hasError
              ? "border-destructive bg-destructive/5 focus-visible:border-destructive"
              : isFocused
              ? "border-ring bg-background"
              : "border-input bg-background hover:border-ring/50",

            // Disabled state
            disabled && "opacity-50 cursor-not-allowed bg-muted",

            // Complete state indicator
            isComplete &&
              !hasError &&
              "ring-1 ring-green-500/20 border-green-500/50",

            className
          )}
          {...props}
        />

        {/* Validation hint tooltip */}
        {showValidationHints && isFocused && (
          <div
            id={`knx-${field}-hint`}
            className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 px-2 py-1 bg-popover text-popover-foreground text-xs rounded-md shadow-md border z-10 whitespace-nowrap"
          >
            {rule.label}: {rule.min}-{rule.max}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn("flex flex-col gap-2", className)}>
      <div className="flex items-center gap-1">
        {renderInput("area", "2")}
        <span className="text-muted-foreground font-medium select-none">/</span>
        {renderInput("line", "1")}
        <span className="text-muted-foreground font-medium select-none">/</span>
        {renderInput("device", "3")}
      </div>

      {/* Status indicator */}
      {hasInteracted && (
        <div className="flex items-center gap-2 text-xs">
          {isComplete ? (
            <span className="text-green-600 flex items-center gap-1">
              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              Valid KNX address
            </span>
          ) : (
            <span className="text-muted-foreground">
              Format: Area/Line/Device (e.g., 1/2/3)
            </span>
          )}
        </div>
      )}
    </div>
  );
};
